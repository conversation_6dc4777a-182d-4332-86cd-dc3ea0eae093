{"version": 4, "routes": {"/api/health": {"initialHeaders": {"content-type": "application/json", "x-next-cache-tags": "_N_T_/layout,_N_T_/api/layout,_N_T_/api/health/layout,_N_T_/api/health/route,_N_T_/api/health"}, "experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/api/health", "dataRoute": null}, "/en": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/[locale]", "dataRoute": "/en.rsc"}, "/es": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/[locale]", "dataRoute": "/es.rsc"}, "/hi": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/[locale]", "dataRoute": "/hi.rsc"}, "/ja": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/[locale]", "dataRoute": "/ja.rsc"}, "/pt": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/[locale]", "dataRoute": "/pt.rsc"}, "/zh": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/[locale]", "dataRoute": "/zh.rsc"}}, "dynamicRoutes": {"/[locale]": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "routeRegex": "^/([^/]+?)(?:/)?$", "dataRoute": "/[locale].rsc", "fallback": null, "dataRouteRegex": "^/([^/]+?)\\.rsc$"}}, "notFoundRoutes": [], "preview": {"previewModeId": "08ba9ebc0f0b46e32ead46db9cec49da", "previewModeSigningKey": "da5ab3f809c3bc43d860b8709c1ed99fc8ad92d8d99ddb227927f206b7af1197", "previewModeEncryptionKey": "ad1a9a00f90f69d6dc52d26047c7608e6db851376da3b2d9ba053eaf9417c809"}}