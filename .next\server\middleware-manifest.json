{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|robots.txt|sitemap.xml|images|icons|fonts).*))(.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|images|icons|fonts).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "duqhUPENcD4qLuwaEEoja", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "GG+hMKeC06vYCGIWZsDBcWFj03XgXgaHlhpuwdryOTw=", "__NEXT_PREVIEW_MODE_ID": "08ba9ebc0f0b46e32ead46db9cec49da", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "ad1a9a00f90f69d6dc52d26047c7608e6db851376da3b2d9ba053eaf9417c809", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "da5ab3f809c3bc43d860b8709c1ed99fc8ad92d8d99ddb227927f206b7af1197"}}}, "functions": {}, "sortedMiddleware": ["/"]}